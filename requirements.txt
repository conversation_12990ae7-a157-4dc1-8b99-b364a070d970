# Streamlit Cloud Deployment Requirements
# Core Streamlit and web framework
streamlit>=1.28.0

# Data processing and analysis
pandas>=1.5.0
numpy>=1.21.0

# Machine Learning & NLP (optimized for cloud deployment)
torch>=2.0.0
transformers>=4.20.0
tokenizers>=0.12.0
scikit-learn>=1.0.0

# Facebook API & Web Requests
requests>=2.25.0
facebook-sdk>=3.1.0

# Environment & Configuration
python-dotenv>=0.19.0

# Additional utilities for UI
Pillow>=8.0.0
plotly>=5.0.0

# For better performance in cloud deployment
psutil>=5.8.0
