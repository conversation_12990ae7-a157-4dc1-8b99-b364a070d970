# Bab 3. Peran<PERSON><PERSON> dan Implementasi

... (bagian sebelumnya) ...

### 3.2.6. <PERSON><PERSON><PERSON> 6: Deployment (Penyebaran)

Tahap *deployment* adalah fase terakhir dalam metodologi CRISP-DM. Pada tahap ini, model yang telah dievaluasi dan dianggap memuaskan akan diintegrasikan ke dalam lingkungan produksi atau operasional agar dapat digunakan oleh pengguna akhir. <PERSON>ju<PERSON> utamanya adalah untuk mewujudkan nilai bisnis dari hasil pemodelan data yang telah dilakukan.

Kegiatan dalam tahap ini tidak hanya sebatas meluncurkan model, tetapi juga mencakup perencanaan, pemantauan, dan pemeliharaan. Proses ini memastikan bahwa model dapat berjalan secara efektif dan efisien dalam jangka panjang.

Berdasarkan metodologi CRISP-DM, langkah-langkah utama dalam tahap *deployment* meliputi:
1.  **Perencanaan Deployment (*Plan Deployment*):** Merencanakan bagaimana model akan diimplementasikan secara teknis dan organisasional. Ini termasuk menentukan infrastruktur yang dibutuhkan dan bagaimana pengguna akan berinteraksi dengan model.
2.  **Perencanaan Pemantauan dan Pemeliharaan (*Plan Monitoring and Maintenance*):** Merancang strategi untuk memantau kinerja model secara berkala dan merencanakan tindakan pemeliharaan jika terjadi penurunan performa atau masalah teknis.
3.  **Produksi Laporan Akhir (*Produce Final Report*):** Mendokumentasikan seluruh proses proyek, mulai dari pemahaman bisnis hingga hasil deployment. Laporan ini berfungsi sebagai catatan komprehensif dari proyek yang telah dilaksanakan.
4.  **Tinjauan Proyek (*Review Project*):** Melakukan evaluasi akhir terhadap keseluruhan proyek untuk mengidentifikasi keberhasilan, kegagalan, dan pembelajaran yang dapat diambil untuk proyek di masa depan.

#### Implementasi Tahap Deployment pada Proyek Ini

Dalam proyek ini, model deteksi spam yang telah dibangun diimplementasikan dalam bentuk **aplikasi web interaktif**. Aplikasi ini berfungsi sebagai antarmuka (*user interface*) yang memungkinkan pengguna untuk menguji teks atau judul secara langsung dan mendapatkan hasil klasifikasi (spam atau bukan spam) dari model.

Teknologi utama yang digunakan untuk deployment adalah **Streamlit**, sebuah *framework* Python yang memungkinkan pembuatan aplikasi web untuk *data science* dengan cepat dan efisien.

#### Artefak Implementasi Deployment

Berikut adalah file-file kunci yang dibuat dan digunakan untuk menunjang tahap deployment pada proyek ini:

*   **`streamlit_app.py`**:
    Ini adalah skrip utama yang menjadi titik masuk (*entry point*) aplikasi Streamlit. File ini berisi semua kode yang mendefinisikan tata letak antarmuka pengguna (UI), komponen interaktif (seperti tombol dan area teks), serta logika untuk memanggil model *machine learning* dan menampilkan hasilnya kepada pengguna.

*   **`run.py`**:
    Sebuah skrip pembungkus (*wrapper script*) yang bertujuan untuk menyederhanakan proses penjalankan aplikasi. Skrip ini biasanya berisi perintah untuk menjalankan file `streamlit_app.py` menggunakan *command-line interface* (CLI) dari Streamlit.

*   **`start.bat`**:
    File *batch* untuk sistem operasi Windows yang menyediakan cara mudah bagi pengguna untuk meluncurkan aplikasi dengan satu kali klik. File ini menjalankan skrip `run.py` atau perintah `streamlit run` secara langsung, sehingga menyembunyikan kompleksitas perintah di *terminal*.

*   **`requirements.txt`**:
    File ini sangat krusial untuk deployment. Isinya adalah daftar semua pustaka (*library*) Python beserta versinya yang dibutuhkan oleh proyek agar dapat berjalan dengan benar. Sebelum aplikasi dijalankan di lingkungan baru, file ini digunakan untuk menginstal semua dependensi yang diperlukan melalui perintah `pip install -r requirements.txt`.

*   **`.streamlit/config.toml`**:
    File konfigurasi untuk aplikasi Streamlit. Di sini, pengembang dapat mengatur berbagai parameter aplikasi, seperti tema (terang/gelap), warna utama, dan pengaturan server lainnya untuk menyesuaikan tampilan dan perilaku aplikasi.

*   **`src/services/spam_detector.py` atau `spam_detector_optimized.py`**:
    Meskipun dikembangkan pada tahap *modeling*, file ini adalah inti dari layanan prediksi. Aplikasi Streamlit (`streamlit_app.py`) akan mengimpor dan menggunakan fungsi atau kelas dari skrip ini untuk memproses input dari pengguna dan menghasilkan prediksi.

*   **`src/models/...`**:
    Seluruh direktori `models` yang berisi file-file model (seperti `model.safetensors`, `config.json`, `vocab.txt`) adalah artefak vital yang harus disertakan dalam deployment. Aplikasi web akan memuat model ini untuk melakukan inferensi.

### *******. Perancangan Antarmuka dan Fitur Aplikasi

Untuk mewujudkan *deployment* yang fungsional, aplikasi web ini dirancang dengan struktur multi-halaman (*multi-page app*). Pendekatan ini memungkinkan pemisahan logika untuk setiap fitur ke dalam modul-modul yang independen. Navigasi utama diatur oleh skrip `app_controller.py` atau `dashboard.py`, yang berfungsi sebagai titik pusat navigasi (*router*) untuk memuat halaman-halaman fitur yang tersimpan dalam direktori `src/app/page_modules/`.

Setiap file dalam direktori `page_modules` merepresentasikan satu halaman atau fitur spesifik dalam aplikasi. Berikut adalah rincian perancangan dan fungsi dari setiap modul:

*   **`dashboard.py` (Halaman Utama)**
    *   **Tujuan:** Memberikan gambaran umum (overview) dan ringkasan status sistem secara *real-time*.
    *   **Implementasi Fitur:**
        *   Menampilkan statistik kunci, seperti jumlah total teks yang diperiksa, persentase spam yang terdeteksi, dan aktivitas terbaru.
        *   Menyajikan visualisasi data, misalnya grafik tren deteksi spam dari waktu ke waktu.
        *   Berfungsi sebagai halaman arahan (*landing page*) setelah pengguna masuk.
    <details>
    <summary>Kode Lengkap: src/app/dashboard.py</summary>

    ```python
    #!/usr/bin/env python3
    """
    Dashboard Module
    Contains dashboard-specific rendering logic and recent activity display
    """

    import streamlit as st
    from typing import Dict, Optional
    from datetime import datetime
    from src.app.ui_components import NotificationManager, render_comment_card


    class DashboardRenderer:
        """Handles dashboard rendering and recent activity display"""
        
        def __init__(self, facebook_api, spam_detector, confidence_threshold: float):
            self.facebook_api = facebook_api
            self.spam_detector = spam_detector
            self.confidence_threshold = confidence_threshold
        
        def render_dashboard(self):
            """Render main dashboard - ONLY dashboard content, NO logs components"""
            # CRITICAL: This method should NEVER render logs components
            # All logs components should ONLY be in render_logs() method

            # Use isolated container with unique key to prevent bleeding
            dashboard_key = "dashboard_main_container"

            # Clear any existing dashboard state to prevent bleeding
            if f"{dashboard_key}_state" in st.session_state:
                del st.session_state[f"{dashboard_key}_state"]

            dashboard_container = st.container(key=dashboard_key)
            with dashboard_container:
                # Header with auto-refresh status
                col_header, col_status = st.columns([3, 1])

                with col_header:
                    st.markdown('<h1 class="main-header">🛡️ Judol Remover</h1>', unsafe_allow_html=True)

                with col_status:
                    # Show auto-refresh status
                    if (st.session_state.get('monitor_running', False) and
                        st.session_state.get('auto_refresh_enabled', False)):
                        refresh_count = st.session_state.get('refresh_counter', 0)
                        st.success(f"🔄 Auto Refresh ON (#{refresh_count})")
                    elif st.session_state.get('monitor_running', False):
                        st.warning("⏸️ Auto Refresh OFF")
                    else:
                        st.info("⏹️ Monitor Stopped")

                # Metrics row - isolated within dashboard container
                col1, col2, col3, col4, col5 = st.columns(5)

            with col1:
                st.metric(
                    "Comments Processed",
                    st.session_state.statistics['comments_processed'],
                    delta=None
                )

            with col2:
                spam_detected = st.session_state.statistics.get('spam_detected', 0)
                st.metric(
                    "Spam Detected",
                    spam_detected,
                    delta=None
                )

            with col3:
                st.metric(
                    "Spam Removed",
                    st.session_state.statistics['spam_removed'],
                    delta=None
                )

            with col4:
                pending_count = len(st.session_state.get('pending_spam', []))
                st.metric(
                    "Pending Review",
                    pending_count,
                    delta=None,
                    help="Spam comments waiting for manual review"
                )

            with col5:
                status = "🟢 Running" if st.session_state.monitor_running else "🔴 Stopped"
                st.metric("Monitor Status", status)

            # Show auto delete status
            auto_delete_status = "🟢 Enabled" if st.session_state.auto_delete_enabled else "🔴 Disabled"
            st.info(f"Auto Delete: {auto_delete_status}")

            # Sync pending spam from auto monitor
            if 'auto_monitor' in st.session_state and st.session_state.auto_monitor is not None:
                try:
                    st.session_state.auto_monitor.sync_pending_spam_to_session_state()
                except Exception:
                    pass

            # Show pending spam alert
            pending_count = len(st.session_state.get('pending_spam', []))
            if pending_count > 0 and not st.session_state.auto_delete_enabled:
                st.warning(f"⚠️ {pending_count} spam comments are waiting for manual review. Go to 'Pending Spam' page to review them.")

            st.markdown("---")

            # Recent posts and comments - ONLY Facebook posts, NO logs
            self.render_recent_activity()
        
        def render_recent_activity(self):
            """Render recent posts and comments - DASHBOARD ONLY, NO LOGS"""
            # CRITICAL: This method is for dashboard only
            # It should NEVER render logs, activity logs, or monitoring logs
            # All logs should ONLY be rendered in render_logs() method

            st.markdown("### 📝 Recent Posts & Comments")

            # ONLY render Facebook posts and comments - NO logs components
            self.render_posts_and_comments()

        def render_posts_and_comments(self):
            """Render recent posts and comments from Facebook - FACEBOOK ONLY, NO LOGS"""
            # CRITICAL GUARD: This method should ONLY render Facebook posts and comments
            # It should NEVER render any logs, activity logs, monitoring logs, or log buttons
            # All logs components belong ONLY in render_logs() method

            if not self.facebook_api:
                st.warning("⚠️ Facebook API not connected. Please check your access token.")
                return

            # Refresh button for Facebook posts only with unique key
            if st.button("🔄 Refresh Posts", key="dashboard_refresh_posts_btn"):
                st.session_state.posts_cache = {}
                st.session_state.comments_cache = {}
                # Use experimental_rerun to avoid component conflicts
                st.experimental_rerun() if hasattr(st, 'experimental_rerun') else st.rerun()

            try:
                # Get recent posts from Facebook
                posts = self.facebook_api.get_recent_posts(limit=5)

                if not posts:
                    st.info("No recent posts found.")
                    return

                # Display Facebook posts with collapsible comments
                for post in posts:
                    with st.expander(f"📄 Post from {post.get('created_time', 'Unknown time')}", expanded=False):
                        # Post content
                        if post.get('message'):
                            st.markdown(f"**Content:** {post['message'][:200]}...")

                        # Load comments for this post
                        self.render_post_comments(post['id'])

            except Exception as e:
                st.error(f"❌ Error loading posts: {str(e)}")

            # CRITICAL: NO logs rendering code should be here
            # This method is for Facebook posts ONLY
        
        def render_post_comments(self, post_id: str):
            """Render comments for a specific post"""
            try:
                # Get comments
                comments = self.facebook_api.get_post_comments(post_id, limit=10)
                
                if not comments:
                    st.info("No comments found for this post.")
                    return
                
                st.markdown(f"**Comments ({len(comments)}):**")
                
                # Process each comment
                for comment in comments:
                    self.render_comment_with_detection(comment, post_id)
                    
            except Exception as e:
                st.error(f"❌ Error loading comments: {str(e)}")
        
        def render_comment_with_detection(self, comment: Dict, post_id: str):
            """Render individual comment with spam detection"""
            comment_id = comment['id']
            message = comment.get('message', '')
            author = comment.get('from', {}).get('name', 'Unknown')
            
            # Get spam prediction
            try:
                prediction = self.spam_detector.predict(message)
                is_spam = prediction['is_spam'] and prediction['confidence'] > self.confidence_threshold
            except Exception as e:
                prediction = {'is_spam': False, 'confidence': 0.0, 'label': 'error', 'error': str(e)}
                is_spam = False
            
            # Use the reusable comment card component
            render_comment_card(
                comment=comment,
                post_id=post_id,
                is_spam=is_spam,
                prediction=prediction,
                confidence_threshold=self.confidence_threshold,
                facebook_api=self.facebook_api,
                spam_detector=self.spam_detector,
                delete_callback=self.delete_comment
            )
        
        def delete_comment(self, comment_id: str, post_id: str, message: str, author: str, reason: str = "Manual deletion"):
            """Delete a comment"""
            try:
                success = self.facebook_api.delete_comment(comment_id)
                if success:
                    st.session_state.statistics['spam_removed'] += 1
                    NotificationManager.show_notification(f"Deleted comment by {author} ({reason})", "success", 4000)

                    # Log the deletion
                    if 'monitor_logs' not in st.session_state:
                        st.session_state.monitor_logs = []

                    log_entry = {
                        'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                        'action': 'DELETED',
                        'comment_id': comment_id,
                        'author': author,
                        'message': message[:100],
                        'post_id': post_id,
                        'reason': reason
                    }
                    st.session_state.monitor_logs.append(log_entry)

                    # Clear cache to refresh comments
                    if post_id in st.session_state.comments_cache:
                        del st.session_state.comments_cache[post_id]
                    # Use experimental_rerun to avoid component conflicts
                    st.experimental_rerun() if hasattr(st, 'experimental_rerun') else st.rerun()
                else:
                    NotificationManager.show_notification("Failed to delete comment", "error", 5000)
            except Exception as e:
                NotificationManager.show_notification(f"Error deleting comment: {str(e)}", "error", 5000)
    ```
    </details>

*   **`manual_check.py` (Halaman Cek Manual)**
    *   **Tujuan:** Menyediakan fungsionalitas inti dari aplikasi, yaitu memungkinkan pengguna untuk menguji sebuah teks secara langsung.
    *   **Implementasi Fitur:**
        *   Terdapat sebuah area input teks (`st.text_area`) di mana pengguna dapat memasukkan atau menempelkan judul atau konten yang ingin dianalisis.
        *   Sebuah tombol "Periksa" (`st.button`) akan memicu proses analisis.
        *   Hasil klasifikasi dari model (*Spam* atau *Bukan Spam*) akan ditampilkan secara jelas kepada pengguna, seringkali disertai dengan skor kepercayaan (*confidence score*).
    <details>
    <summary>Kode Lengkap: src/app/page_modules/manual_check.py</summary>

    ```python
    #!/usr/bin/env python3
    """
    Manual Check Page Module
    Handles manual spam checking functionality
    """

    import streamlit as st
    from datetime import datetime
    from typing import Dict, List
    from src.app.ui_components import NotificationManager


    class ManualCheckPage:
        """Handles manual check page rendering and functionality"""
        
        def __init__(self, facebook_api, spam_detector, confidence_threshold: float):
            self.facebook_api = facebook_api
            self.spam_detector = spam_detector
            self.confidence_threshold = confidence_threshold
        
        def render(self):
            """Render manual check page"""
            # Use isolated container with unique key to prevent bleeding
            manual_check_key = "manual_check_main_container"

            # Clear any existing manual check state to prevent bleeding
            if f"{manual_check_key}_state" in st.session_state:
                del st.session_state[f"{manual_check_key}_state"]

            manual_check_container = st.container(key=manual_check_key)
            with manual_check_container:
                st.markdown("### 🔍 Manual Comment Check")

                if not self.facebook_api:
                    st.warning("⚠️ Facebook API not connected.")
                    st.info("💡 Please configure your Facebook API credentials in the Settings page.")
                    st.markdown("---")
                    st.markdown("#### 📋 Manual Check Interface Preview")
                    st.selectbox("Choose a post (Preview)", ["Connect Facebook API to see posts"], disabled=True)
                    st.button("🔍 Check Post (Disabled)", disabled=True)
                    return

                # Post selection
                st.markdown("#### Select Post to Check")

                try:
                    posts = self.facebook_api.get_recent_posts(limit=10)

                    if not posts:
                        st.info("No posts found.")
                        return

                    # Create post options
                    post_options = {}
                    for post in posts:
                        preview = post.get('message', 'No message')[:50] + "..."
                        created_time = post.get('created_time', 'Unknown time')
                        option_text = f"{created_time} - {preview}"
                        post_options[option_text] = post['id']

                    selected_post_text = st.selectbox("Choose a post:", list(post_options.keys()))
                    selected_post_id = post_options[selected_post_text]

                    col1, col2 = st.columns([1, 4])

                    with col1:
                        if st.button("🔍 Check Post", type="primary"):
                            with st.spinner("Checking post for spam comments..."):
                                # Perform manual check without depending on AutoMonitor
                                results = self.perform_manual_check(selected_post_id)
                                st.session_state.manual_check_results = results

                    # Display results
                    if 'manual_check_results' in st.session_state:
                        results = st.session_state.manual_check_results

                        st.markdown("#### 📊 Check Results")

                        col1, col2, col3, col4 = st.columns(4)
                        with col1:
                            st.metric("Comments Checked", results['comments_checked'])
                        with col2:
                            st.metric("Spam Found", results['spam_found'])
                        with col3:
                            st.metric("Spam Removed", results['spam_removed'])
                        with col4:
                            st.metric("Errors", results['errors'])

                        # Detailed results
                        if results.get('details'):
                            st.markdown("#### 📝 Detailed Results")

                            for detail in results['details']:
                                emoji = "🚨" if detail['is_spam'] else "✅"
                                status = "DELETED" if detail.get('deleted') else ("SPAM" if detail['is_spam'] else "NORMAL")

                                with st.expander(f"{emoji} {detail['author']} - {status}"):
                                    st.write(f"**Message:** {detail['message']}")
                                    st.write(f"**Confidence:** {detail['confidence']:.3f}")
                                    st.write(f"**Comment ID:** {detail['comment_id']}")
                                    if detail.get('deleted'):
                                        st.success("✅ Comment deleted successfully")

                except Exception as e:
                    st.error(f"❌ Error in manual check: {str(e)}")

        def perform_manual_check(self, post_id: str) -> Dict:
            """Perform manual spam check on a post without AutoMonitor dependency"""
            try:
                # Get comments for the post
                comments = self.facebook_api.get_post_comments(post_id, limit=50)

                results = {
                    'comments_checked': 0,
                    'spam_found': 0,
                    'spam_removed': 0,
                    'errors': 0,
                    'details': []
                }

                if not comments:
                    return results

                for comment in comments:
                    try:
                        comment_id = comment['id']
                        message = comment.get('message', '')
                        author = comment.get('from', {}).get('name', 'Unknown')

                        # Skip empty messages
                        if not message.strip():
                            continue

                        results['comments_checked'] += 1

                        # Get spam prediction
                        prediction = self.spam_detector.predict(message)
                        is_spam = prediction['is_spam'] and prediction['confidence'] > self.confidence_threshold

                        detail = {
                            'comment_id': comment_id,
                            'author': author,
                            'message': message,
                            'is_spam': is_spam,
                            'confidence': prediction['confidence'],
                            'label': prediction['label'],
                            'deleted': False
                        }

                        if is_spam:
                            results['spam_found'] += 1

                            # Only delete if auto-delete is enabled
                            if st.session_state.auto_delete_enabled:
                                try:
                                    success = self.facebook_api.delete_comment(comment_id)
                                    if success:
                                        results['spam_removed'] += 1
                                        detail['deleted'] = True

                                        # Log the deletion
                                        self._log_deletion({
                                            'comment_id': comment_id,
                                            'author': author,
                                            'message': message,
                                            'post_id': post_id,
                                            'prediction': prediction
                                        }, "Manual check deletion")

                                except Exception as e:
                                    results['errors'] += 1
                                    detail['error'] = str(e)
                            else:
                                # Add to pending spam if auto-delete is disabled
                                if 'pending_spam' not in st.session_state:
                                    st.session_state.pending_spam = []

                                pending_item = {
                                    'comment_id': comment_id,
                                    'author': author,
                                    'message': message,
                                    'post_id': post_id,
                                    'prediction': prediction,
                                    'detected_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                                }

                                # Check if not already in pending
                                if not any(p['comment_id'] == comment_id for p in st.session_state.pending_spam):
                                    st.session_state.pending_spam.append(pending_item)

                        results['details'].append(detail)

                    except Exception as e:
                        results['errors'] += 1
                        results['details'].append({
                            'comment_id': comment.get('id', 'unknown'),
                            'author': comment.get('from', {}).get('name', 'Unknown'),
                            'message': comment.get('message', ''),
                            'is_spam': False,
                            'confidence': 0.0,
                            'label': 'error',
                            'deleted': False,
                            'error': str(e)
                        })

                return results

            except Exception as e:
                return {
                    'comments_checked': 0,
                    'spam_found': 0,
                    'spam_removed': 0,
                    'errors': 1,
                    'details': [],
                    'error': str(e)
                }

        def _log_deletion(self, comment: Dict, reason: str):
            """Helper function to log comment deletion"""
            if 'monitor_logs' not in st.session_state:
                st.session_state.monitor_logs = []

            log_entry = {
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'action': 'DELETED',
                'comment_id': comment['comment_id'],
                'author': comment['author'],
                'message': comment['message'][:100],
                'post_id': comment['post_id'],
                'reason': reason
            }
            st.session_state.monitor_logs.append(log_entry)

            # Update statistics
            st.session_state.statistics['spam_removed'] += 1
    ```
    </details>

*   **`pending_spam.py` (Halaman Spam Tertunda)**
    *   **Tujuan:** Mengelola kasus-kasus di mana model mendeteksi spam tetapi memerlukan verifikasi manual (tinjauan oleh manusia).
    *   **Implementasi Fitur:**
        *   Menampilkan daftar atau tabel berisi data yang ditandai sebagai "spam tertunda".
        *   Menyediakan tombol aksi bagi pengguna (misalnya, seorang admin) untuk mengonfirmasi ("Benar Spam") atau menolak ("Bukan Spam") klasifikasi dari model.
        *   Fitur ini penting untuk *human-in-the-loop system*, di mana umpan balik dari pengguna dapat digunakan untuk meningkatkan kualitas model di masa depan.
    <details>
    <summary>Kode Lengkap: src/app/page_modules/pending_spam.py</summary>

    ```python
    #!/usr/bin/env python3
    """
    Pending Spam Page Module
    Handles pending spam comments review and management
    """

    import streamlit as st
    from datetime import datetime
    from typing import Dict, List
    from src.app.ui_components import NotificationManager


    class PendingSpamPage:
        """Handles pending spam page rendering and functionality"""
        
        def __init__(self, facebook_api):
            self.facebook_api = facebook_api
        
        def render(self):
            """Render pending spam page for manual review"""
            # Use isolated container with unique key to prevent bleeding
            pending_spam_key = "pending_spam_main_container"

            # Clear any existing pending spam state to prevent bleeding
            if f"{pending_spam_key}_state" in st.session_state:
                del st.session_state[f"{pending_spam_key}_state"]

            pending_spam_container = st.container(key=pending_spam_key)
            with pending_spam_container:
                st.markdown("### 🚨 Pending Spam Comments")
                st.markdown("Komentar yang terdeteksi sebagai spam tapi belum dihapus karena auto-delete dinonaktifkan.")

                # Initialize pending spam if not exists
                if 'pending_spam' not in st.session_state:
                    st.session_state.pending_spam = []

                pending_comments = st.session_state.pending_spam

                if not pending_comments:
                    st.info("📭 Tidak ada komentar spam yang menunggu review.")
                    if not st.session_state.auto_delete_enabled:
                        st.warning("💡 Auto-delete sedang dinonaktifkan. Aktifkan di sidebar untuk menghapus spam secara otomatis.")
                    return

                st.markdown(f"#### 📊 {len(pending_comments)} komentar menunggu review")

                # Bulk actions
                col1, col2 = st.columns([1, 1])

                with col1:
                    if st.button("🗑️ Delete All Spam", type="primary"):
                        deleted_count = 0
                        for comment in pending_comments[:]:  # Copy list to avoid modification during iteration
                            try:
                                success = self.facebook_api.delete_comment(comment['comment_id'])
                                if success:
                                    deleted_count += 1
                                    st.session_state.pending_spam.remove(comment)

                                    # Log the deletion
                                    self._log_deletion(comment, "Bulk manual deletion")

                            except Exception as e:
                                st.error(f"Failed to delete comment {comment['comment_id']}: {str(e)}")

                        NotificationManager.show_notification(f"Deleted {deleted_count} spam comments", "success", 4000)
                        st.rerun()

                with col2:
                    if st.button("✅ Mark All as Normal"):
                        st.session_state.pending_spam = []
                        NotificationManager.show_notification("All comments marked as normal", "info", 3000)
                        st.rerun()

                # Display pending comments
                for i, comment in enumerate(pending_comments):
                    with st.expander(f"🚨 Spam #{i+1} - {comment['author']}", expanded=False):
                        st.markdown(f"**Author:** {comment['author']}")
                        st.markdown(f"**Message:** {comment['message']}")
                        st.markdown(f"**Confidence:** {comment['prediction']['confidence']:.3f}")
                        st.markdown(f"**Detected:** {comment['detected_time']}")

                        # Individual actions
                        col1, col2 = st.columns([1, 1])

                        with col1:
                            if st.button(f"🗑️ Delete", key=f"delete_pending_{i}"):
                                try:
                                    success = self.facebook_api.delete_comment(comment['comment_id'])
                                    if success:
                                        st.session_state.pending_spam.remove(comment)
                                        self._log_deletion(comment, "Manual deletion from pending")
                                        NotificationManager.show_notification("Comment deleted", "success", 3000)
                                        st.rerun()
                                    else:
                                        st.error("❌ Failed to delete comment")
                                except Exception as e:
                                    st.error(f"❌ Error: {str(e)}")

                        with col2:
                            if st.button(f"✅ Mark Normal", key=f"normal_pending_{i}"):
                                st.session_state.pending_spam.remove(comment)
                                NotificationManager.show_notification("Marked as normal", "info", 2000)
                                st.rerun()

                        # Show prediction details
                        st.json(comment['prediction'])

        def _log_deletion(self, comment: Dict, reason: str):
            """Helper function to log comment deletion"""
            if 'monitor_logs' not in st.session_state:
                st.session_state.monitor_logs = []

            log_entry = {
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'action': 'DELETED',
                'comment_id': comment['comment_id'],
                'author': comment['author'],
                'message': comment['message'][:100],
                'post_id': comment['post_id'],
                'reason': reason
            }
            st.session_state.monitor_logs.append(log_entry)

            # Update statistics
            st.session_state.statistics['spam_removed'] += 1
    ```
    </details>

*   **`logs.py` (Halaman Log Aktivitas)**
    *   **Tujuan:** Memberikan catatan historis dari semua aktivitas penting yang terjadi di dalam aplikasi.
    *   **Implementasi Fitur:**
        *   Menampilkan log prediksi: setiap kali model membuat prediksi, entri log dibuat yang mencatat input, output, dan waktu kejadian.
        *   Menampilkan log sistem: mencatat error, peringatan, atau informasi status penting lainnya yang berguna untuk *debugging* dan pemantauan.
        *   Kemungkinan dilengkapi dengan fitur filter berdasarkan tanggal atau jenis log.
    <details>
    <summary>Kode Lengkap: src/app/page_modules/logs.py</summary>

    ```python
    #!/usr/bin/env python3
    """
    Logs Page Module
    Handles activity logs display and management
    """

    import streamlit as st
    import pandas as pd
    import time
    from datetime import datetime
    from typing import List, Dict
    from src.app.ui_components import NotificationManager


    class LogsPage:
        """Handles logs page rendering and functionality"""
        
        def __init__(self):
            pass
        
        def render(self):
            """Render logs page with real-time updates - LOGS PAGE ONLY"""
            # CRITICAL: This method should ONLY be called when current_page == "Logs"
            # If this appears in Dashboard, there's a routing bug

            # Use isolated container with unique key to prevent bleeding to other pages
            logs_key = "logs_main_container"

            # Clear any existing logs page state to prevent bleeding
            if f"{logs_key}_state" in st.session_state:
                del st.session_state[f"{logs_key}_state"]

            logs_container = st.container(key=logs_key)
            with logs_container:
                try:
                    # Real-time header with auto-refresh indicator
                    col1, col2 = st.columns([3, 1])

                    with col1:
                        st.markdown("### 📋 Activity Logs")

                    with col2:
                        # Show auto-refresh status (controlled from sidebar)
                        if (st.session_state.get('monitor_running', False) and
                            st.session_state.get('auto_refresh_enabled', True)):
                            st.success("🔄 Auto Refresh ON")
                        elif st.session_state.get('monitor_running', False):
                            st.warning("⏸️ Auto Refresh OFF")
                        else:
                            st.info("⏹️ Monitor Stopped")

                    # Initialize session state variables
                    self._initialize_session_state()

                    # Show auto-refresh instructions
                    if not st.session_state.get('monitor_running', False):
                        st.info("ℹ️ Start the monitor from the sidebar to see real-time logs")
                    elif not st.session_state.get('auto_refresh_enabled', True):
                        st.warning("⚠️ Auto-refresh is disabled. Enable 'Auto Refresh UI' in the sidebar for real-time updates")
                    else:
                        refresh_count = st.session_state.get('refresh_counter', 0)
                        st.success(f"✅ Real-time logs enabled - Updates every 5 seconds automatically (#{refresh_count})")

                    # Force sync logs from auto monitor on every page load
                    self._sync_logs_from_monitor()

                    # Real-time metrics
                    self._render_metrics()

                    # Real-time log controls
                    self._render_log_controls()

                    # Display logs
                    self._render_logs_display()

                except Exception as e:
                    st.error(f"❌ Error rendering logs page: {str(e)}")
                    st.exception(e)
                    # Show fallback content
                    self._render_fallback_content()

        def _initialize_session_state(self):
            """Initialize all required session state variables"""
            # Initialize logs if not exists
            if 'monitor_logs' not in st.session_state:
                st.session_state.monitor_logs = []

            # Initialize auto refresh enabled if not exists
            if 'auto_refresh_enabled' not in st.session_state:
                st.session_state.auto_refresh_enabled = True

            # Initialize monitor running if not exists
            if 'monitor_running' not in st.session_state:
                st.session_state.monitor_running = False

        def _sync_logs_from_monitor(self):
            """Sync logs from auto monitor with error handling"""
            if ('auto_monitor' in st.session_state and
                st.session_state.auto_monitor is not None and
                st.session_state.get('monitor_running', False)):
                try:
                    if hasattr(st.session_state.auto_monitor, 'sync_logs_to_session_state'):
                        synced_count = st.session_state.auto_monitor.sync_logs_to_session_state()
                        # Show sync status in real-time
                        if synced_count > 0:
                            st.caption(f"🔄 Last sync: {datetime.now().strftime('%H:%M:%S')} - {synced_count} logs")
                    else:
                        # Fallback method
                        if hasattr(st.session_state.auto_monitor, 'get_recent_activity'):
                            recent_logs = st.session_state.auto_monitor.get_recent_activity(100)
                            if recent_logs:
                                st.session_state.monitor_logs = recent_logs
                                st.caption(f"🔄 Fallback sync: {len(recent_logs)} logs loaded")
                except Exception as e:
                    st.warning(f"⚠️ Log sync error: {str(e)}")

        def _render_fallback_content(self):
            """Render fallback content when main rendering fails"""
            st.markdown("### 📋 Activity Logs (Fallback Mode)")
            st.info("📭 Logs page is in fallback mode due to an error.")

            # Show basic log count
            log_count = len(st.session_state.get('monitor_logs', []))
            st.metric("Total Logs", log_count)

            # Show simple log list if available
            if log_count > 0:
                st.markdown("#### Recent Logs")
                for i, log in enumerate(st.session_state.monitor_logs[-10:]):
                    st.text(f"{i+1}. {log.get('timestamp', 'N/A')} - {log.get('action', 'N/A')} - {log.get('author', 'N/A')}")
            else:
                st.info("No logs available")

        def _add_sample_logs(self):
            """Add sample logs for testing"""
            sample_logs = [
                {
                    'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    'action': 'NEW_COMMENT',
                    'comment_id': 'sample_001',
                    'author': 'John Doe',
                    'message': 'This is a normal comment',
                    'post_id': 'post_123',
                    'reason': 'New comment detected'
                },
                {
                    'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    'action': 'SPAM_DETECTED',
                    'comment_id': 'sample_002',
                    'author': 'Spammer',
                    'message': 'Buy cheap products now!!!',
                    'post_id': 'post_123',
                    'reason': 'Spam keywords detected'
                },
                {
                    'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    'action': 'DELETED',
                    'comment_id': 'sample_003',
                    'author': 'BadUser',
                    'message': 'Inappropriate content',
                    'post_id': 'post_456',
                    'reason': 'Auto-deleted spam'
                },
                {
                    'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    'action': 'PENDING_SPAM',
                    'comment_id': 'sample_004',
                    'author': 'SuspiciousUser',
                    'message': 'Suspicious comment content',
                    'post_id': 'post_789',
                    'reason': 'Requires manual review'
                }
            ]

            # Add sample logs to session state
            for log in sample_logs:
                st.session_state.monitor_logs.append(log)

            # Keep only last 100 entries
            if len(st.session_state.monitor_logs) > 100:
                st.session_state.monitor_logs = st.session_state.monitor_logs[-100:]

        def _render_metrics(self):
            """Render real-time metrics"""
            st.markdown("#### 📊 Real-time Metrics")
            col1, col2, col3, col4 = st.columns(4)

            with col1:
                total_logs = len(st.session_state.monitor_logs)
                st.metric("Total Logs", total_logs)

            with col2:
                monitor_status = "🟢 Running" if st.session_state.monitor_running else "🔴 Stopped"
                st.metric("Monitor Status", monitor_status)

            with col3:
                if 'auto_monitor' in st.session_state and st.session_state.auto_monitor is not None:
                    processed_count = len(st.session_state.auto_monitor.processed_comments)
                    internal_logs_count = len(st.session_state.auto_monitor.internal_logs)
                    st.metric("Processed Comments", processed_count)
                else:
                    internal_logs_count = 0
                    st.metric("Processed Comments", "N/A")

            with col4:
                # Show sync status
                if internal_logs_count > 0:
                    sync_status = "🟢 Synced" if internal_logs_count == total_logs else "🟡 Pending"
                    st.metric("Sync Status", sync_status)
                else:
                    st.metric("Sync Status", "N/A")

            # Show latest activity timestamp
            if st.session_state.monitor_logs:
                latest_log = st.session_state.monitor_logs[-1]
                latest_time = latest_log.get('timestamp', 'Unknown')
                st.info(f"🕒 Latest Activity: {latest_time}")
            else:
                st.info("🕒 No activity recorded yet")

        def _render_log_controls(self):
            """Render log control buttons"""
            st.markdown("#### 🎛️ Log Controls")
            col1, col2, col3, col4 = st.columns([1, 1, 1, 1])

            with col1:
                if st.button("🔄 Manual Refresh", key="logs_manual_refresh_btn"):
                    # Force sync from auto monitor
                    if ('auto_monitor' in st.session_state and
                        st.session_state.auto_monitor is not None):
                        try:
                            if hasattr(st.session_state.auto_monitor, 'sync_logs_to_session_state'):
                                synced_count = st.session_state.auto_monitor.sync_logs_to_session_state()
                                NotificationManager.show_notification(f"Refreshed! Synced {synced_count} logs", "success", 2000)
                        except Exception as e:
                            NotificationManager.show_notification(f"Error: {str(e)}", "error", 3000)
                    st.rerun()

            with col2:
                if st.button("🗑️ Clear Logs", key="logs_clear_logs_btn"):
                    st.session_state.monitor_logs = []
                    # Also clear internal logs if available
                    if ('auto_monitor' in st.session_state and
                        st.session_state.auto_monitor is not None):
                        try:
                            st.session_state.auto_monitor.internal_logs = []
                        except Exception:
                            pass
                    NotificationManager.show_notification("All logs cleared!", "info", 2000)
                    st.rerun()

            with col3:
                if st.button("🔄 Force Sync", key="logs_force_sync_btn"):
                    if ('auto_monitor' in st.session_state and
                        st.session_state.auto_monitor is not None):
                        try:
                            if hasattr(st.session_state.auto_monitor, 'sync_logs_to_session_state'):
                                synced_count = st.session_state.auto_monitor.sync_logs_to_session_state()
                                NotificationManager.show_notification(f"Force synced {synced_count} logs", "success", 2000)
                            else:
                                NotificationManager.show_notification("Sync method not available", "warning", 2000)
                        except Exception as e:
                            NotificationManager.show_notification(f"Sync error: {str(e)}", "error", 3000)
                    else:
                        NotificationManager.show_notification("Auto monitor not available", "warning", 2000)
                    st.rerun()

            with col4:
                if st.button("🧪 Add Test Log", key="logs_add_test_log_btn"):
                    test_log = {
                        'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                        'action': 'TEST',
                        'comment_id': f'test_{int(time.time())}',
                        'author': 'Test User',
                        'message': f'Test log entry at {datetime.now().strftime("%H:%M:%S")}',
                        'post_id': 'test_post',
                        'reason': 'Manual test'
                    }
                    st.session_state.monitor_logs.append(test_log)
                    NotificationManager.show_notification("Test log added!", "success", 2000)
                    st.rerun()

        def _render_logs_display(self):
            """Render the main logs display"""
            logs = st.session_state.monitor_logs

            # Force refresh logs from auto monitor
            if st.button("🔄 Force Sync Logs from Monitor"):
                if 'auto_monitor' in st.session_state and st.session_state.auto_monitor is not None:
                    try:
                        # Check if method exists (for backward compatibility)
                        if hasattr(st.session_state.auto_monitor, 'sync_logs_to_session_state'):
                            total_logs = st.session_state.auto_monitor.sync_logs_to_session_state()
                            st.success(f"Synced logs from auto monitor. Total logs: {total_logs}")
                        else:
                            # Fallback: get recent activity and replace session logs
                            recent_activity = st.session_state.auto_monitor.get_recent_activity(50)
                            if recent_activity:
                                st.session_state.monitor_logs = recent_activity
                                st.success(f"Synced {len(recent_activity)} logs from auto monitor (fallback method)")
                            else:
                                st.warning("No logs found in auto monitor")
                    except Exception as e:
                        st.error(f"Error syncing logs: {e}")
                        # Show restart option
                        if st.button("🔄 Reset Auto Monitor"):
                            st.session_state.auto_monitor = None
                            st.success("Auto monitor reset. Please restart it from the sidebar.")
                            st.rerun()
                else:
                    st.warning("Auto monitor not available")
                st.rerun()

            if not logs:
                st.info("📭 No activity logs yet. Start the auto monitor to see activity.")

                # Troubleshooting tips
                st.markdown("**Troubleshooting:**")
                st.write("1. Check if auto monitor is running")
                st.write("2. Try 'Force Sync Logs from Monitor' button above")
                st.write("3. Use 'Add Test Log' button below to add sample data")

                # Add test log buttons for empty state
                col1, col2, col3 = st.columns([1, 1, 2])
                with col1:
                    if st.button("🧪 Add Test Log", key="empty_state_test_log"):
                        test_log = {
                            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                            'action': 'TEST',
                            'comment_id': f'test_{int(time.time())}',
                            'author': 'Test User',
                            'message': f'Test log entry at {datetime.now().strftime("%H:%M:%S")}',
                            'post_id': 'test_post',
                            'reason': 'Manual test'
                        }
                        st.session_state.monitor_logs.append(test_log)
                        NotificationManager.show_notification("Test log added!", "success", 2000)
                        st.rerun()

                with col2:
                    if st.button("📊 Add Sample Logs", key="add_sample_logs"):
                        self._add_sample_logs()
                        NotificationManager.show_notification("Sample logs added!", "success", 2000)
                        st.rerun()
                return

            st.markdown(f"#### Recent Activity ({len(logs)} entries)")

            # Filter options
            col1, col2 = st.columns(2)

            with col1:
                log_filter = st.selectbox(
                    "Filter by action:",
                    ["All", "NEW_COMMENT", "SPAM_DETECTED", "PENDING_SPAM", "DELETED", "TEST", "ERROR", "INFO"]
                )

            with col2:
                show_count = st.number_input(
                    "Show last N entries:",
                    min_value=10,
                    max_value=100,
                    value=50,
                    step=10
                )

            # Debug info with expandable details
            with st.expander("🔍 Debug Information", expanded=False):
                st.write(f"**Total logs:** {len(logs)}")
                st.write(f"**Filter:** {log_filter}")
                st.write(f"**Monitor running:** {st.session_state.get('monitor_running', False)}")
                st.write(f"**Auto monitor available:** {'auto_monitor' in st.session_state and st.session_state.auto_monitor is not None}")

                if logs:
                    st.write(f"**Sample log actions:** {[log.get('action', 'NO_ACTION') for log in logs[:3]]}")
                    st.write("**First log structure:**")
                    st.json(logs[0] if logs else {})
                else:
                    st.write("**No logs available for debugging**")

            # Filter and display logs
            filtered_logs = logs
            if log_filter != "All":
                filtered_logs = [log for log in logs if log.get('action') == log_filter]

            # Show most recent entries
            recent_logs = filtered_logs[-show_count:]
            recent_logs.reverse()  # Show newest first

            # Display logs directly in table format
            st.markdown("---")

            if recent_logs:
                # Show table directly without tabs
                self._render_table_view(recent_logs)
            else:
                st.info(f"📭 No logs found for filter: {log_filter}")
                # Show empty table structure
                import pandas as pd
                empty_df = pd.DataFrame({
                    'Time': [],
                    'Action': [],
                    'Author': [],
                    'Message': [],
                    'Reason': []
                })
                st.dataframe(empty_df, use_container_width=True, height=200)



        def _render_table_view(self, recent_logs: List[Dict]):
            """Render logs in table format with error handling"""
            try:
                # Table format for quick overview
                log_data = []

                # Handle empty logs case
                if not recent_logs:
                    st.info("📊 No logs to display in table format")
                    # Show empty table structure
                    empty_df = pd.DataFrame({
                        'Time': [],
                        'Action': [],
                        'Author': [],
                        'Message': [],
                        'Reason': []
                    })
                    st.dataframe(empty_df, use_container_width=True, height=200)
                    return

                # Process logs with error handling
                for i, log in enumerate(recent_logs):
                    try:
                        # Validate log structure
                        if not isinstance(log, dict):
                            st.warning(f"⚠️ Invalid log entry at index {i}: {type(log)}")
                            continue

                        # Add emoji based on action
                        action = log.get('action', 'UNKNOWN')
                        if action == 'NEW_COMMENT':
                            action_display = "💬 NEW_COMMENT"
                        elif action == 'SPAM_DETECTED':
                            action_display = "🚨 SPAM_DETECTED"
                        elif action == 'DELETED':
                            action_display = "🗑️ DELETED"
                        elif action == 'PENDING_SPAM':
                            action_display = "⏳ PENDING_SPAM"
                        elif action == 'TEST':
                            action_display = "🧪 TEST"
                        else:
                            action_display = f"ℹ️ {action}"

                        # Safe string processing
                        timestamp = log.get('timestamp', '')
                        time_display = timestamp[-8:] if timestamp and len(timestamp) >= 8 else timestamp

                        author = str(log.get('author', ''))
                        author_display = author[:20] + "..." if len(author) > 20 else author

                        message = str(log.get('message', ''))
                        message_display = message[:40] + "..." if len(message) > 40 else message

                        reason = str(log.get('reason', ''))
                        reason_display = reason[:30] + "..." if len(reason) > 30 else reason

                        log_data.append({
                            'Time': time_display,
                            'Action': action_display,
                            'Author': author_display,
                            'Message': message_display,
                            'Reason': reason_display
                        })

                    except Exception as e:
                        st.warning(f"⚠️ Error processing log entry {i}: {str(e)}")
                        continue

                # Create and display dataframe
                if log_data:
                    df = pd.DataFrame(log_data)
                    st.dataframe(
                        df[['Time', 'Action', 'Author', 'Message', 'Reason']],
                        use_container_width=True,
                        height=400
                    )
                else:
                    st.info("📭 No valid log entries to display")
                    # Show empty table structure
                    empty_df = pd.DataFrame({
                        'Time': [],
                        'Action': [],
                        'Author': [],
                        'Message': [],
                        'Reason': []
                    })
                    st.dataframe(empty_df, use_container_width=True, height=200)

            except Exception as e:
                st.error(f"❌ Error rendering table view: {str(e)}")
                st.exception(e)
                # Fallback: show simple text list
                st.markdown("#### Fallback: Simple Log List")
                for i, log in enumerate(recent_logs[:10]):
                    st.text(f"{i+1}. {log}")
    ```
    </details>

*   **`settings.py` (Halaman Pengaturan)**
    *   **Tujuan:** Memungkinkan pengguna untuk mengonfigurasi perilaku aplikasi sesuai kebutuhan.
    *   **Implementasi Fitur:**
        *   Pengaturan ambang batas (*threshold*): Pengguna dapat menyesuaikan tingkat sensitivitas model dalam mengklasifikasikan spam.
        *   Manajemen notifikasi: Mengaktifkan atau menonaktifkan pemberitahuan.
        *   Pengaturan koneksi (misalnya, API key jika terhubung ke layanan eksternal).
    <details>
    <summary>Kode Lengkap: src/app/page_modules/settings.py</summary>

    ```python
    #!/usr/bin/env python3
    """
    Settings Page Module
    Handles application settings and configuration
    """

    import streamlit as st
    from src.app.ui_components import NotificationManager
    from src.services.spam_detector import SpamDetector


    class SettingsPage:
        """Handles settings page rendering and functionality"""
        
        def __init__(self, page_id: str, page_access_token: str, model_path: str, confidence_threshold: float):
            self.page_id = page_id
            self.page_access_token = page_access_token
            self.model_path = model_path
            self.confidence_threshold = confidence_threshold
        
        def render(self):
            """Render settings page"""
            # Use isolated container with unique key to prevent bleeding
            settings_key = "settings_main_container"

            # Clear any existing settings state to prevent bleeding
            if f"{settings_key}_state" in st.session_state:
                del st.session_state[f"{settings_key}_state"]

            settings_container = st.container(key=settings_key)
            with settings_container:
                st.markdown("### ⚙️ Settings")

                # Facebook API Settings
                st.markdown("#### 📘 Facebook API Configuration")

                col1, col2 = st.columns(2)

                with col1:
                    page_id = st.text_input("Page ID", value=self.page_id or "", help="Facebook Page ID")

                with col2:
                    page_access_token = st.text_input(
                        "Page Access Token",
                        value=self.page_access_token or "",
                        type="password",
                        help="Facebook Page Access Token"
                    )

                if st.button("💾 Update Facebook Settings"):
                    if page_id and page_access_token:
                        try:
                            # Test new credentials
                            from src.app.streamlit_facebook import FacebookAPI
                            test_api = FacebookAPI(page_id, page_access_token)

                            # Update session state
                            st.session_state.facebook_api = test_api
                            self.page_id = page_id
                            self.page_access_token = page_access_token

                            NotificationManager.show_notification("Facebook API settings updated successfully!", "success", 4000)
                        except Exception as e:
                            NotificationManager.show_notification(f"Failed to update Facebook settings: {str(e)}", "error", 6000)
                    else:
                        st.warning("Please provide both Page ID and Access Token")

                st.markdown("---")

                # Spam Detection Settings
                st.markdown("#### 🤖 Spam Detection Configuration")

                confidence_threshold = st.slider(
                    "Confidence Threshold",
                    min_value=0.0,
                    max_value=1.0,
                    value=self.confidence_threshold,
                    step=0.05,
                    help="Minimum confidence required to classify and delete as spam"
                )

                model_path = st.text_input(
                    "Model Path",
                    value=self.model_path,
                    help="Path to the IndoBERT model directory"
                )

                if st.button("💾 Update Detection Settings"):
                    self.confidence_threshold = confidence_threshold
                    st.session_state.confidence_threshold = confidence_threshold

                    if model_path != self.model_path:
                        try:
                            # Reload model with new path
                            with st.spinner("Reloading spam detection model..."):
                                st.session_state.spam_detector = SpamDetector(model_path)
                            self.model_path = model_path
                            NotificationManager.show_notification("Model reloaded successfully!", "success", 4000)
                        except Exception as e:
                            NotificationManager.show_notification(f"Failed to reload model: {str(e)}", "error", 6000)
                    else:
                        NotificationManager.show_notification("Detection settings updated!", "success", 3000)

                st.markdown("---")

                # Monitor Settings
                st.markdown("#### 🔄 Auto Monitor Configuration")

                poll_interval = st.number_input(
                    "Poll Interval (seconds)",
                    min_value=10,
                    max_value=300,
                    value=30,
                    step=5,
                    help="How often to check for new comments"
                )

                auto_delete = st.checkbox(
                    "Auto Delete Spam",
                    value=True,
                    help="Automatically delete comments detected as spam"
                )

                if st.button("💾 Update Monitor Settings"):
                    st.session_state.poll_interval = poll_interval
                    st.session_state.auto_delete = auto_delete
                    NotificationManager.show_notification("Monitor settings updated!", "success", 3000)

                st.markdown("---")

                # System Information
                st.markdown("#### 📊 System Information")

                col1, col2 = st.columns(2)

                with col1:
                    st.markdown("**Model Status:**")
                    if st.session_state.spam_detector:
                        st.success("🟢 Loaded")
                        st.write(f"Model Path: `{self.model_path}`")
                    else:
                        st.error("🔴 Not Loaded")

                with col2:
                    st.markdown("**Facebook API Status:**")
                    if st.session_state.facebook_api:
                        st.success("🟢 Connected")
                        st.write(f"Page ID: `{self.page_id}`")
                    else:
                        st.error("🔴 Not Connected")

                # Cache Management
                st.markdown("---")
                st.markdown("#### 🗂️ Cache Management")

                col1, col2, col3 = st.columns(3)

                with col1:
                    posts_cache_size = len(st.session_state.posts_cache)
                    st.metric("Posts Cache", posts_cache_size)

                with col2:
                    comments_cache_size = len(st.session_state.comments_cache)
                    st.metric("Comments Cache", comments_cache_size)

                with col3:
                    if st.button("🗑️ Clear Cache"):
                        st.session_state.posts_cache = {}
                        st.session_state.comments_cache = {}
                        NotificationManager.show_notification("Cache cleared!", "info", 2000)
                        st.rerun()
    ```
    </details>

*   **`test_detector.py` (Halaman Uji Detektor)**
    *   **Tujuan:** Menyediakan antarmuka khusus untuk pengujian model yang lebih mendalam, biasanya ditujukan untuk pengembang atau *power user*.
    *   **Implementasi Fitur:**
        *   Berbeda dari "Cek Manual", halaman ini mungkin memungkinkan pengujian *batch* (mengunggah file berisi banyak teks untuk diuji sekaligus).
        *   Menampilkan informasi diagnostik yang lebih detail dari model, seperti skor mentah (*raw scores*) atau waktu yang dibutuhkan untuk inferensi (*inference time*).
    <details>
    <summary>Kode Lengkap: src/app/page_modules/test_detector.py</summary>

    ```python
    #!/usr/bin/env python3
    """
    Test Detector Page Module
    Handles spam detector testing functionality
    """

    import streamlit as st
    import pandas as pd
    from typing import List, Dict


    class TestDetectorPage:
        """Handles test detector page rendering and functionality"""
        
        def __init__(self, spam_detector, confidence_threshold: float):
            self.spam_detector = spam_detector
            self.confidence_threshold = confidence_threshold
        
        def render(self):
            """Render spam detector test page"""
            # Use isolated container with unique key to prevent bleeding
            test_detector_key = "test_detector_main_container"

            # Clear any existing test detector state to prevent bleeding
            if f"{test_detector_key}_state" in st.session_state:
                del st.session_state[f"{test_detector_key}_state"]

            test_detector_container = st.container(key=test_detector_key)
            with test_detector_container:
                st.markdown("### 🧪 Test Spam Detector")

                if not self.spam_detector:
                    st.warning("⚠️ Spam detector not loaded.")
                    st.info("💡 The spam detector will be loaded automatically when the model is available.")
                    st.markdown("---")
                    st.markdown("#### 📋 Test Interface Preview")
                    st.text_area("Test Text (Preview)", placeholder="Enter comment text to test for spam detection...", disabled=True)
                    st.slider("Confidence Threshold (Preview)", 0.0, 1.0, 0.5, disabled=True)
                    st.button("🔍 Test Detection (Disabled)", disabled=True)
                    return

                # Test input
                st.markdown("#### Enter text to test:")
                test_text = st.text_area(
                    "Test Text",
                    placeholder="Enter comment text to test for spam detection...",
                    height=100
                )

                # Confidence threshold
                confidence_threshold = st.slider(
                    "Confidence Threshold",
                    min_value=0.0,
                    max_value=1.0,
                    value=self.confidence_threshold,
                    step=0.05,
                    help="Minimum confidence required to classify as spam"
                )

                if st.button("🔍 Test Detection", type="primary"):
                    if test_text.strip():
                        with st.spinner("Analyzing text..."):
                            try:
                                prediction = self.spam_detector.predict(test_text)

                                # Display results
                                st.markdown("#### 📊 Detection Results")

                                col1, col2, col3 = st.columns(3)

                                with col1:
                                    label = prediction['label']
                                    emoji = "🚨" if prediction['is_spam'] else "✅"
                                    st.metric("Classification", f"{emoji} {label.upper()}")

                                with col2:
                                    confidence = prediction['confidence']
                                    st.metric("Confidence", f"{confidence:.3f}")

                                with col3:
                                    is_spam_threshold = prediction['is_spam'] and confidence > confidence_threshold
                                    action = "DELETE" if is_spam_threshold else "KEEP"
                                    color = "🔴" if is_spam_threshold else "🟢"
                                    st.metric("Action", f"{color} {action}")

                                # Detailed information
                                st.markdown("#### 📋 Detailed Information")
                                st.json(prediction)

                                # Explanation
                                if prediction['is_spam']:
                                    if confidence > confidence_threshold:
                                        st.error(f"🚨 This comment would be **DELETED** (confidence {confidence:.3f} > threshold {confidence_threshold})")
                                    else:
                                        st.warning(f"⚠️ Detected as spam but confidence {confidence:.3f} is below threshold {confidence_threshold}")
                                else:
                                    st.success("✅ This comment would be **KEPT** (classified as normal)")

                            except Exception as e:
                                st.error(f"❌ Error testing detection: {str(e)}")
                    else:
                        st.warning("Please enter some text to test.")

                # Batch testing
                st.markdown("---")
                st.markdown("#### 📝 Batch Testing")

                batch_text = st.text_area(
                    "Batch Test (one comment per line)",
                    placeholder="Enter multiple comments, one per line...",
                    height=150
                )

                if st.button("🔍 Test Batch", type="secondary"):
                    if batch_text.strip():
                        lines = [line.strip() for line in batch_text.split('\n') if line.strip()]

                        if lines:
                            with st.spinner(f"Testing {len(lines)} comments..."):
                                results = self._process_batch_test(lines, confidence_threshold)

                                # Display results table
                                df = pd.DataFrame(results)
                                st.dataframe(
                                    df[['text', 'label', 'confidence', 'action']],
                                    use_container_width=True
                                )

                                # Summary
                                spam_count = sum(1 for r in results if r['is_spam'] and r['confidence'] > confidence_threshold)
                                st.info(f"📊 Summary: {spam_count} out of {len(results)} comments would be deleted as spam")

        def _process_batch_test(self, lines: List[str], confidence_threshold: float) -> List[Dict]:
            """Process batch testing of multiple comments"""
            results = []

            for line in lines:
                try:
                    prediction = self.spam_detector.predict(line)
                    results.append({
                        'text': line[:50] + "..." if len(line) > 50 else line,
                        'full_text': line,
                        'is_spam': prediction['is_spam'],
                        'confidence': prediction['confidence'],
                        'label': prediction['label'],
                        'action': 'DELETE' if prediction['is_spam'] and prediction['confidence'] > confidence_threshold else 'KEEP'
                    })
                except Exception as e:
                    results.append({
                        'text': line[:50] + "..." if len(line) > 50 else line,
                        'full_text': line,
                        'is_spam': False,
                        'confidence': 0.0,
                        'label': 'error',
                        'action': 'ERROR',
                        'error': str(e)
                    })

            return results
    ```
    </details>

