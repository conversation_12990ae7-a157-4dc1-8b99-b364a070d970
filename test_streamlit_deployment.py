#!/usr/bin/env python3
"""
Test script to verify Streamlit deployment compatibility
"""

import sys
import importlib.util

def test_imports():
    """Test if all required packages can be imported"""
    required_packages = [
        'streamlit',
        'pandas', 
        'numpy',
        'torch',
        'transformers',
        'tokenizers',
        'sklearn',
        'requests',
        'dotenv',
        'PIL',
        'plotly',
        'psutil'
    ]
    
    failed_imports = []
    
    for package in required_packages:
        try:
            if package == 'PIL':
                import PIL
            elif package == 'sklearn':
                import sklearn
            elif package == 'dotenv':
                import dotenv
            else:
                __import__(package)
            print(f"✅ {package} - OK")
        except ImportError as e:
            print(f"❌ {package} - FAILED: {e}")
            failed_imports.append(package)
    
    return failed_imports

def test_torch_compatibility():
    """Test PyTorch compatibility"""
    try:
        import torch
        print(f"✅ PyTorch version: {torch.__version__}")
        print(f"✅ CUDA available: {torch.cuda.is_available()}")
        
        # Test basic tensor operations
        x = torch.tensor([1.0, 2.0, 3.0])
        y = torch.tensor([4.0, 5.0, 6.0])
        z = x + y
        print(f"✅ Basic tensor operations: {z}")
        
        return True
    except Exception as e:
        print(f"❌ PyTorch test failed: {e}")
        return False

def test_transformers_compatibility():
    """Test transformers library compatibility"""
    try:
        from transformers import AutoTokenizer, AutoModelForSequenceClassification
        print("✅ Transformers imports - OK")
        
        # Test if we can load a simple model (without actually loading the large model)
        print("✅ Transformers compatibility - OK")
        return True
    except Exception as e:
        print(f"❌ Transformers test failed: {e}")
        return False

def main():
    """Run all compatibility tests"""
    print("🔍 Testing Streamlit deployment compatibility...\n")
    
    print("1. Testing package imports:")
    failed_imports = test_imports()
    
    print("\n2. Testing PyTorch compatibility:")
    torch_ok = test_torch_compatibility()
    
    print("\n3. Testing Transformers compatibility:")
    transformers_ok = test_transformers_compatibility()
    
    print("\n" + "="*50)
    if not failed_imports and torch_ok and transformers_ok:
        print("🎉 All tests passed! Deployment should work.")
        return 0
    else:
        print("❌ Some tests failed. Check the issues above.")
        if failed_imports:
            print(f"Failed imports: {', '.join(failed_imports)}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
